# 工作流引擎

一个轻量级的工作流引擎，支持动态插件开发和运行，通过注解自动生成表单界面。

## 项目特点

- 🚀 **轻量级**: 比n8n更简单，但功能足够强大
- 🔧 **动态插件**: 支持在线编写和运行TypeScript/JavaScript插件
- 📝 **注解驱动**: 使用`@property`注解自动生成表单界面
- 🎯 **类型安全**: 支持多种数据类型（string, number, boolean, object, array）
- 🔄 **生命周期**: 插件支持create/run/exit生命周期管理
- 🎨 **现代界面**: 基于Vue3 + NaiveUI的现代化界面

## 技术栈

### 后端
- **Node.js** + **TypeScript** + **Express**
- **SQLite** 数据存储
- **vm2** 插件沙箱运行环境
- **RESTful API** 设计

### 前端
- **Vue3** + **TypeScript** + **Vite**
- **NaiveUI** 组件库
- **Monaco Editor** 代码编辑器
- **Pinia** 状态管理

## 项目结构

```
workflow-engine/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── controllers/     # API控制器
│   │   ├── services/        # 业务逻辑
│   │   ├── models/          # 数据模型
│   │   ├── middleware/      # 中间件
│   │   ├── types/           # 类型定义
│   │   └── utils/           # 工具函数
│   ├── examples/            # 示例插件
│   └── data/                # 数据库文件
├── frontend/                # 前端应用
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── stores/          # 状态管理
│   │   ├── api/             # API接口
│   │   └── types/           # 类型定义
└── ARCHITECTURE.md          # 架构设计文档
```

## 快速开始

### 1. 启动后端服务

```bash
cd backend
npm install
npm run dev
```

后端服务将在 http://localhost:3000 启动

### 2. 启动前端应用

```bash
cd frontend
npm install
npm run dev
```

前端应用将在 http://localhost:5173 启动

### 3. 访问应用

打开浏览器访问 http://localhost:5173，即可开始使用工作流引擎。

## 插件开发

### 插件结构

每个插件都需要实现三个生命周期方法：

```typescript
// 插件生命周期：初始化
async function create(): Promise<void> {
  // 插件初始化逻辑
}

// 插件生命周期：执行
async function run(input: any): Promise<any> {
  // 插件主要逻辑
  return {
    success: true,
    data: { /* 输出数据 */ },
    logs: [ /* 执行日志 */ ]
  };
}

// 插件生命周期：清理
async function exit(): Promise<void> {
  // 插件清理逻辑
}
```

### 属性注解

使用`@property`注解定义插件参数：

```typescript
@property(type: 'string', label: '输入文本', required: true, description: '需要处理的文本内容')
let inputText: string;

@property(type: 'number', label: '最大长度', required: false, default: 100, description: '文本最大长度')
let maxLength: number;

@property(type: 'boolean', label: '启用处理', required: false, default: true, description: '是否启用文本处理')
let enabled: boolean;
```

### 支持的数据类型

- `string`: 字符串
- `number`: 数字
- `boolean`: 布尔值
- `object`: JSON对象
- `array`: 数组

## API文档

### 插件管理

- `GET /api/plugins` - 获取插件列表
- `GET /api/plugins/:id` - 获取插件详情
- `POST /api/plugins` - 创建插件
- `PUT /api/plugins/:id` - 更新插件
- `DELETE /api/plugins/:id` - 删除插件

### 插件执行

- `POST /api/plugins/:id/run` - 运行插件
- `GET /api/plugins/:id/properties` - 获取插件属性定义

### 工作流管理

- `GET /api/workflows` - 获取工作流列表（开发中）
- `POST /api/workflows` - 创建工作流（开发中）

## 示例插件

### 字符串处理插件

```typescript
@property(type: 'string', label: '输入文本', required: true, description: '需要处理的文本')
let inputText: string;

@property(type: 'string', label: '操作类型', required: true, default: 'uppercase', description: '处理类型：uppercase, lowercase, reverse')
let operation: string;

async function create(): Promise<void> {
  console.log('字符串处理插件初始化');
}

async function run(input: any): Promise<any> {
  let result = inputText;
  
  switch (operation) {
    case 'uppercase':
      result = result.toUpperCase();
      break;
    case 'lowercase':
      result = result.toLowerCase();
      break;
    case 'reverse':
      result = result.split('').reverse().join('');
      break;
  }
  
  return {
    success: true,
    data: {
      original: inputText,
      processed: result,
      operation: operation
    },
    logs: [`处理完成: ${result}`]
  };
}

async function exit(): Promise<void> {
  console.log('字符串处理插件退出');
}
```

## 开发计划

- [x] 基础插件系统
- [x] 注解解析和表单生成
- [x] 插件运行环境
- [x] 前端界面
- [ ] 工作流编排
- [ ] 插件市场
- [ ] 版本管理
- [ ] 监控告警

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
