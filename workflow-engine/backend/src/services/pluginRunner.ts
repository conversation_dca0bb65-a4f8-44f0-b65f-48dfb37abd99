import { VM } from 'vm2';
import { PluginMetadata, PluginResult, Plugin } from '../types';
import { AppError } from '../middleware/errorHandler';

export class PluginRunner {
  private readonly timeout: number = 30000; // 30秒超时
  private readonly memoryLimit: number = 128; // 128MB内存限制

  async execute(plugin: PluginMetadata, parameters: Record<string, any>): Promise<PluginResult> {
    const startTime = Date.now();
    const logs: string[] = [];

    try {
      // 验证参数
      this.validateParameters(plugin, parameters);

      // 创建沙箱环境
      const vm = new VM({
        timeout: this.timeout,
        sandbox: {
          console: {
            log: (...args: any[]) => logs.push(`[LOG] ${args.join(' ')}`),
            error: (...args: any[]) => logs.push(`[ERROR] ${args.join(' ')}`),
            warn: (...args: any[]) => logs.push(`[WARN] ${args.join(' ')}`),
            info: (...args: any[]) => logs.push(`[INFO] ${args.join(' ')}`),
          },
          JSON,
          Date,
          Math,
          setTimeout,
          clearTimeout,
          setInterval,
          clearInterval,
          // 添加参数到沙箱
          ...parameters,
        },
      });

      // 构建插件代码
      const pluginCode = this.buildPluginCode(plugin.code);
      
      // 执行插件
      const pluginInstance: Plugin = vm.run(pluginCode);

      // 执行插件生命周期
      await pluginInstance.create();
      logs.push('[SYSTEM] 插件初始化完成');

      const result = await pluginInstance.run(parameters);
      logs.push('[SYSTEM] 插件执行完成');

      await pluginInstance.exit();
      logs.push('[SYSTEM] 插件清理完成');

      const executionTime = Date.now() - startTime;

      return {
        success: true,
        data: result.data,
        logs: [...logs, ...result.logs],
        metadata: {
          executionTime,
          timestamp: new Date(),
        },
      };

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      
      logs.push(`[ERROR] 插件执行失败: ${error.message}`);

      return {
        success: false,
        data: null,
        error: error.message,
        logs,
        metadata: {
          executionTime,
          timestamp: new Date(),
        },
      };
    }
  }

  private validateParameters(plugin: PluginMetadata, parameters: Record<string, any>): void {
    for (const property of plugin.properties) {
      const value = parameters[property.name];

      // 检查必填参数
      if (property.required && (value === undefined || value === null)) {
        throw new AppError(`缺少必填参数: ${property.name}`);
      }

      // 类型验证
      if (value !== undefined && value !== null) {
        this.validateParameterType(property.name, value, property.type);
      }
    }
  }

  private validateParameterType(name: string, value: any, expectedType: string): void {
    switch (expectedType) {
      case 'string':
        if (typeof value !== 'string') {
          throw new AppError(`参数 ${name} 应该是字符串类型`);
        }
        break;
      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          throw new AppError(`参数 ${name} 应该是数字类型`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new AppError(`参数 ${name} 应该是布尔类型`);
        }
        break;
      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          throw new AppError(`参数 ${name} 应该是对象类型`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          throw new AppError(`参数 ${name} 应该是数组类型`);
        }
        break;
    }
  }

  private buildPluginCode(userCode: string): string {
    // 移除@property注解，因为它们只用于前端表单生成
    const cleanCode = userCode.replace(/@property\s*\([^)]*\)\s*/g, '');

    return `
      ${cleanCode}

      // 确保插件实现了必要的接口
      if (typeof create !== 'function') {
        throw new Error('插件必须实现 create() 方法');
      }
      if (typeof run !== 'function') {
        throw new Error('插件必须实现 run() 方法');
      }
      if (typeof exit !== 'function') {
        throw new Error('插件必须实现 exit() 方法');
      }

      // 返回插件实例
      ({
        create,
        run,
        exit
      });
    `;
  }
}
