import { v4 as uuidv4 } from 'uuid';
import { database } from '../models/database';
import { PluginMetadata, PluginProperty, PluginResult } from '../types';
import { AppError } from '../middleware/errorHandler';
import { PluginRunner } from './pluginRunner';

export class PluginService {
  private pluginRunner: PluginRunner;

  constructor() {
    this.pluginRunner = new PluginRunner();
  }

  async createPlugin(pluginData: Omit<PluginMetadata, 'id' | 'createdAt' | 'updatedAt'>): Promise<PluginMetadata> {
    const id = uuidv4();
    const now = new Date();

    // 解析插件属性
    const properties = this.parsePluginProperties(pluginData.code);

    const plugin: PluginMetadata = {
      ...pluginData,
      id,
      properties,
      createdAt: now,
      updatedAt: now,
    };

    await database.run(
      `INSERT INTO plugins (id, name, description, version, author, code, properties, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        plugin.id,
        plugin.name,
        plugin.description,
        plugin.version,
        plugin.author,
        plugin.code,
        JSON.stringify(plugin.properties),
        plugin.createdAt.toISOString(),
        plugin.updatedAt.toISOString(),
      ]
    );

    return plugin;
  }

  async getPlugins(): Promise<PluginMetadata[]> {
    const rows = await database.all<any>(
      'SELECT * FROM plugins ORDER BY created_at DESC'
    );

    return rows.map(this.mapRowToPlugin);
  }

  async getPlugin(id: string): Promise<PluginMetadata> {
    const row = await database.get<any>(
      'SELECT * FROM plugins WHERE id = ?',
      [id]
    );

    if (!row) {
      throw new AppError('插件不存在', 404);
    }

    return this.mapRowToPlugin(row);
  }

  async updatePlugin(id: string, updates: Partial<PluginMetadata>): Promise<PluginMetadata> {
    const existing = await this.getPlugin(id);
    
    // 如果更新了代码，重新解析属性
    if (updates.code) {
      updates.properties = this.parsePluginProperties(updates.code);
    }

    const updatedPlugin = {
      ...existing,
      ...updates,
      updatedAt: new Date(),
    };

    await database.run(
      `UPDATE plugins SET 
       name = ?, description = ?, version = ?, author = ?, 
       code = ?, properties = ?, updated_at = ?
       WHERE id = ?`,
      [
        updatedPlugin.name,
        updatedPlugin.description,
        updatedPlugin.version,
        updatedPlugin.author,
        updatedPlugin.code,
        JSON.stringify(updatedPlugin.properties),
        updatedPlugin.updatedAt.toISOString(),
        id,
      ]
    );

    return updatedPlugin;
  }

  async deletePlugin(id: string): Promise<void> {
    const result = await database.run('DELETE FROM plugins WHERE id = ?', [id]);
    
    if (result.changes === 0) {
      throw new AppError('插件不存在', 404);
    }
  }

  async runPlugin(id: string, parameters: Record<string, any>): Promise<PluginResult> {
    const plugin = await this.getPlugin(id);
    return this.pluginRunner.execute(plugin, parameters);
  }

  private parsePluginProperties(code: string): PluginProperty[] {
    const properties: PluginProperty[] = [];

    // 改进的正则表达式，支持多行和更灵活的格式
    const propertyRegex = /@property\s*\(\s*([^)]+)\s*\)\s*\n?\s*let\s+(\w+)\s*:/g;
    let match;

    while ((match = propertyRegex.exec(code)) !== null) {
      const [, params, name] = match;

      try {
        // 解析参数字符串
        const property = this.parsePropertyParams(params, name);
        properties.push(property);
        console.log(`解析到属性: ${name}`, property);
      } catch (error) {
        console.warn(`解析属性 ${name} 失败:`, error);
      }
    }

    console.log(`总共解析到 ${properties.length} 个属性`);
    return properties;
  }

  private parsePropertyParams(params: string, name: string): PluginProperty {
    // 改进的参数解析，支持更多格式
    const property: PluginProperty = {
      name,
      type: 'string',
    };

    // 解析type参数 - 支持带引号和不带引号的格式
    const typeMatch = params.match(/type:\s*['"]?([^'",\s]+)['"]?/);
    if (typeMatch) {
      property.type = typeMatch[1] as any;
    }

    // 解析label参数
    const labelMatch = params.match(/label:\s*['"]([^'"]+)['"]/);
    if (labelMatch) {
      property.label = labelMatch[1];
    }

    // 解析required参数
    const requiredMatch = params.match(/required:\s*(true|false)/);
    if (requiredMatch) {
      property.required = requiredMatch[1] === 'true';
    }

    // 解析default参数
    const defaultMatch = params.match(/default:\s*(['"]?)([^'",\s]+)\1/);
    if (defaultMatch) {
      const defaultValue = defaultMatch[2];
      // 根据类型转换默认值
      switch (property.type) {
        case 'number':
          property.default = parseFloat(defaultValue);
          break;
        case 'boolean':
          property.default = defaultValue === 'true';
          break;
        default:
          property.default = defaultValue;
      }
    }

    // 解析description参数
    const descMatch = params.match(/description:\s*['"]([^'"]+)['"]/);
    if (descMatch) {
      property.description = descMatch[1];
    }

    return property;
  }

  private mapRowToPlugin(row: any): PluginMetadata {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      version: row.version,
      author: row.author,
      code: row.code,
      properties: JSON.parse(row.properties || '[]'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }
}
