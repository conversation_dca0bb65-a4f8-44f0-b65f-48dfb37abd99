import { Router, Request, Response } from 'express';
import { PluginService } from '../services/pluginService';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse } from '../types';

const router = Router();
const pluginService = new PluginService();

// 获取插件列表
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const plugins = await pluginService.getPlugins();
  
  const response: ApiResponse = {
    success: true,
    data: plugins,
    message: '获取插件列表成功',
  };
  
  res.json(response);
}));

// 获取插件详情
router.get('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const plugin = await pluginService.getPlugin(id);
  
  const response: ApiResponse = {
    success: true,
    data: plugin,
    message: '获取插件详情成功',
  };
  
  res.json(response);
}));

// 创建插件
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const { name, description, version, author, code } = req.body;
  
  if (!name || !code) {
    throw new AppError('插件名称和代码不能为空', 400);
  }
  
  const plugin = await pluginService.createPlugin({
    name,
    description: description || '',
    version: version || '1.0.0',
    author: author || '',
    code,
    properties: [], // 将在service中解析
  });
  
  const response: ApiResponse = {
    success: true,
    data: plugin,
    message: '创建插件成功',
  };
  
  res.status(201).json(response);
}));

// 更新插件
router.put('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updates = req.body;
  
  const plugin = await pluginService.updatePlugin(id, updates);
  
  const response: ApiResponse = {
    success: true,
    data: plugin,
    message: '更新插件成功',
  };
  
  res.json(response);
}));

// 删除插件
router.delete('/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  await pluginService.deletePlugin(id);
  
  const response: ApiResponse = {
    success: true,
    message: '删除插件成功',
  };
  
  res.json(response);
}));

// 运行插件
router.post('/:id/run', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { parameters = {} } = req.body;
  
  const result = await pluginService.runPlugin(id, parameters);
  
  const response: ApiResponse = {
    success: true,
    data: result,
    message: '插件执行完成',
  };
  
  res.json(response);
}));

// 获取插件属性定义
router.get('/:id/properties', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const plugin = await pluginService.getPlugin(id);
  
  const response: ApiResponse = {
    success: true,
    data: plugin.properties,
    message: '获取插件属性成功',
  };
  
  res.json(response);
}));

export default router;
