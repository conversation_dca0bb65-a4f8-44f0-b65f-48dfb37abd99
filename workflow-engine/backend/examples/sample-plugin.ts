// 示例插件：字符串处理插件
// 演示@property注解的使用和插件生命周期

@property(type: 'string', label: '输入文本', required: true, description: '需要处理的文本内容')
let inputText: string;

@property(type: 'string', label: '操作类型', required: true, default: 'uppercase', description: '处理类型：uppercase, lowercase, reverse')
let operation: string;

@property(type: 'boolean', label: '去除空格', required: false, default: false, description: '是否去除文本中的空格')
let trimSpaces: boolean;

@property(type: 'number', label: '最大长度', required: false, default: 100, description: '处理后文本的最大长度')
let maxLength: number;

// 插件状态
let isInitialized = false;
let processedCount = 0;

// 插件生命周期：初始化
async function create(): Promise<void> {
  console.log('字符串处理插件初始化中...');
  isInitialized = true;
  processedCount = 0;
  console.log('插件初始化完成');
}

// 插件生命周期：执行
async function run(input: any): Promise<any> {
  if (!isInitialized) {
    throw new Error('插件未初始化');
  }

  console.log('开始处理文本:', inputText);
  
  let result = inputText;
  
  // 根据操作类型处理文本
  switch (operation) {
    case 'uppercase':
      result = result.toUpperCase();
      break;
    case 'lowercase':
      result = result.toLowerCase();
      break;
    case 'reverse':
      result = result.split('').reverse().join('');
      break;
    default:
      throw new Error(`不支持的操作类型: ${operation}`);
  }
  
  // 去除空格
  if (trimSpaces) {
    result = result.replace(/\s+/g, '');
  }
  
  // 限制长度
  if (result.length > maxLength) {
    result = result.substring(0, maxLength);
  }
  
  processedCount++;
  console.log(`文本处理完成，第${processedCount}次处理`);
  
  return {
    success: true,
    data: {
      originalText: inputText,
      processedText: result,
      operation: operation,
      trimSpaces: trimSpaces,
      maxLength: maxLength,
      processedCount: processedCount
    },
    logs: [
      `原始文本: ${inputText}`,
      `操作类型: ${operation}`,
      `处理结果: ${result}`,
      `处理次数: ${processedCount}`
    ]
  };
}

// 插件生命周期：清理
async function exit(): Promise<void> {
  console.log(`字符串处理插件退出，共处理了${processedCount}次文本`);
  isInitialized = false;
  processedCount = 0;
}
