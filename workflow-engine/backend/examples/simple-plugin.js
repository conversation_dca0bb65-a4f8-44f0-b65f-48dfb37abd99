// 简单的JavaScript插件，用于测试插件系统

// @property(type: 'string', label: '输入文本', required: true, description: '需要处理的文本')
let inputText;

// @property(type: 'string', label: '操作', required: true, default: 'uppercase', description: '操作类型')
let operation;

// 插件生命周期：初始化
async function create() {
  console.log('简单插件初始化');
}

// 插件生命周期：执行
async function run(input) {
  console.log('开始处理:', inputText);
  
  let result = inputText;
  
  if (operation === 'uppercase') {
    result = result.toUpperCase();
  } else if (operation === 'lowercase') {
    result = result.toLowerCase();
  }
  
  return {
    success: true,
    data: {
      original: inputText,
      processed: result,
      operation: operation
    },
    logs: [
      `处理文本: ${inputText}`,
      `操作: ${operation}`,
      `结果: ${result}`
    ]
  };
}

// 插件生命周期：清理
async function exit() {
  console.log('简单插件退出');
}
