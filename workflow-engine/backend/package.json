{"name": "workflow-engine-backend", "version": "1.0.0", "description": "轻量级工作流引擎后端", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["workflow", "engine", "plugin"], "author": "", "license": "MIT", "dependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.10", "@types/node": "^24.0.10", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "nodemon": "^3.1.10", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "uuid": "^11.1.0", "vm2": "^3.9.19"}}