import axios from 'axios';
import type { 
  ApiResponse, 
  PluginMetadata, 
  PluginExecutionRequest, 
  PluginResult,
  Workflow,
  WorkflowExecutionRequest 
} from '@/types';

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API错误:', error.response?.data || error.message);
    return Promise.reject(error.response?.data || error);
  }
);

// 插件相关API
export const pluginApi = {
  // 获取插件列表
  getPlugins: (): Promise<ApiResponse<PluginMetadata[]>> => 
    api.get('/plugins'),

  // 获取插件详情
  getPlugin: (id: string): Promise<ApiResponse<PluginMetadata>> => 
    api.get(`/plugins/${id}`),

  // 创建插件
  createPlugin: (plugin: Omit<PluginMetadata, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<PluginMetadata>> => 
    api.post('/plugins', plugin),

  // 更新插件
  updatePlugin: (id: string, plugin: Partial<PluginMetadata>): Promise<ApiResponse<PluginMetadata>> => 
    api.put(`/plugins/${id}`, plugin),

  // 删除插件
  deletePlugin: (id: string): Promise<ApiResponse<void>> => 
    api.delete(`/plugins/${id}`),

  // 运行插件
  runPlugin: (request: PluginExecutionRequest): Promise<ApiResponse<PluginResult>> => 
    api.post(`/plugins/${request.pluginId}/run`, { parameters: request.parameters }),

  // 获取插件属性定义
  getPluginProperties: (id: string): Promise<ApiResponse<any>> => 
    api.get(`/plugins/${id}/properties`),
};

// 工作流相关API
export const workflowApi = {
  // 获取工作流列表
  getWorkflows: (): Promise<ApiResponse<Workflow[]>> => 
    api.get('/workflows'),

  // 获取工作流详情
  getWorkflow: (id: string): Promise<ApiResponse<Workflow>> => 
    api.get(`/workflows/${id}`),

  // 创建工作流
  createWorkflow: (workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Workflow>> => 
    api.post('/workflows', workflow),

  // 更新工作流
  updateWorkflow: (id: string, workflow: Partial<Workflow>): Promise<ApiResponse<Workflow>> => 
    api.put(`/workflows/${id}`, workflow),

  // 删除工作流
  deleteWorkflow: (id: string): Promise<ApiResponse<void>> => 
    api.delete(`/workflows/${id}`),

  // 运行工作流
  runWorkflow: (request: WorkflowExecutionRequest): Promise<ApiResponse<PluginResult[]>> => 
    api.post(`/workflows/${request.workflowId}/run`, { initialData: request.initialData }),
};

export default api;
