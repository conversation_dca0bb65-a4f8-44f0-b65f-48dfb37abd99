<script setup lang="ts">
import { RouterView } from 'vue-router'
import { NLayout, NLayoutHeader, NLayoutContent, NMenu, NSpace, NH1 } from 'naive-ui'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const menuOptions = [
  {
    label: '插件管理',
    key: 'plugins',
    children: [
      {
        label: '插件列表',
        key: 'plugin-list'
      },
      {
        label: '创建插件',
        key: 'plugin-create'
      }
    ]
  },
  {
    label: '工作流',
    key: 'workflows'
  }
]

const handleMenuSelect = (key: string) => {
  switch (key) {
    case 'plugin-list':
      router.push('/plugins')
      break
    case 'plugin-create':
      router.push('/plugins/create')
      break
    case 'workflows':
      router.push('/workflows')
      break
  }
}
</script>

<template>
  <NLayout style="height: 100vh">
    <NLayoutHeader style="height: 64px; padding: 0 24px; display: flex; align-items: center; border-bottom: 1px solid #e0e0e6">
      <NH1 style="margin: 0; color: #2080f0">
        🔧 工作流引擎
      </NH1>
      <div style="flex: 1"></div>
      <NSpace>
        <span style="color: #666">轻量级插件工作流系统</span>
      </NSpace>
    </NLayoutHeader>

    <NLayout has-sider style="height: calc(100vh - 64px)">
      <NLayout style="width: 240px; background: #fafafa">
        <NLayoutContent style="padding: 16px">
          <NMenu
            :options="menuOptions"
            @update:value="handleMenuSelect"
          />
        </NLayoutContent>
      </NLayout>

      <NLayoutContent style="padding: 24px">
        <RouterView />
      </NLayoutContent>
    </NLayout>
  </NLayout>
</template>

<style scoped>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
</style>
