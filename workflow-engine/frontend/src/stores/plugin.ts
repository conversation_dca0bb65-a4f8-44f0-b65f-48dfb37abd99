import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { pluginApi } from '@/api'
import type { PluginMetadata, PluginResult } from '@/types'

export const usePluginStore = defineStore('plugin', () => {
  // 状态
  const plugins = ref<PluginMetadata[]>([])
  const currentPlugin = ref<PluginMetadata | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const pluginCount = computed(() => plugins.value.length)

  // 操作
  const fetchPlugins = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.getPlugins()
      if (response.success) {
        plugins.value = response.data || []
      } else {
        error.value = response.error || '获取插件列表失败'
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
    } finally {
      loading.value = false
    }
  }

  const getPlugin = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.getPlugin(id)
      if (response.success) {
        currentPlugin.value = response.data || null
        return response.data
      } else {
        error.value = response.error || '获取插件详情失败'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
      return null
    } finally {
      loading.value = false
    }
  }

  const createPlugin = async (pluginData: Omit<PluginMetadata, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.createPlugin(pluginData)
      if (response.success && response.data) {
        plugins.value.unshift(response.data)
        return response.data
      } else {
        error.value = response.error || '创建插件失败'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
      return null
    } finally {
      loading.value = false
    }
  }

  const updatePlugin = async (id: string, updates: Partial<PluginMetadata>) => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.updatePlugin(id, updates)
      if (response.success && response.data) {
        const index = plugins.value.findIndex(p => p.id === id)
        if (index !== -1) {
          plugins.value[index] = response.data
        }
        if (currentPlugin.value?.id === id) {
          currentPlugin.value = response.data
        }
        return response.data
      } else {
        error.value = response.error || '更新插件失败'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
      return null
    } finally {
      loading.value = false
    }
  }

  const deletePlugin = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.deletePlugin(id)
      if (response.success) {
        plugins.value = plugins.value.filter(p => p.id !== id)
        if (currentPlugin.value?.id === id) {
          currentPlugin.value = null
        }
        return true
      } else {
        error.value = response.error || '删除插件失败'
        return false
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
      return false
    } finally {
      loading.value = false
    }
  }

  const runPlugin = async (id: string, parameters: Record<string, any>): Promise<PluginResult | null> => {
    loading.value = true
    error.value = null
    try {
      const response = await pluginApi.runPlugin({ pluginId: id, parameters })
      if (response.success) {
        return response.data || null
      } else {
        error.value = response.error || '运行插件失败'
        return null
      }
    } catch (err: any) {
      error.value = err.message || '网络错误'
      return null
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    plugins,
    currentPlugin,
    loading,
    error,
    // 计算属性
    pluginCount,
    // 操作
    fetchPlugins,
    getPlugin,
    createPlugin,
    updatePlugin,
    deletePlugin,
    runPlugin,
    clearError
  }
})
