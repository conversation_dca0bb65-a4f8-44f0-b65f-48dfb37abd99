import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/plugins'
    },
    {
      path: '/plugins',
      name: 'plugins',
      component: () => import('../views/PluginList.vue'),
    },
    {
      path: '/plugins/create',
      name: 'plugin-create',
      component: () => import('../views/PluginEditor.vue'),
    },
    {
      path: '/plugins/:id/edit',
      name: 'plugin-edit',
      component: () => import('../views/PluginEditor.vue'),
    },
    {
      path: '/plugins/:id/run',
      name: 'plugin-run',
      component: () => import('../views/PluginRunner.vue'),
    },
    {
      path: '/workflows',
      name: 'workflows',
      component: () => import('../views/WorkflowList.vue'),
    },
  ],
})

export default router
