// 与后端保持一致的类型定义
export type PropertyType = 'string' | 'number' | 'boolean' | 'object' | 'array';

export interface PluginProperty {
  name: string;
  type: PropertyType;
  label?: string;
  required?: boolean;
  default?: any;
  description?: string;
}

export interface PluginResult {
  success: boolean;
  data: any;
  error?: string;
  logs: string[];
  metadata: {
    executionTime: number;
    timestamp: Date;
  };
}

export interface PluginMetadata {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  code: string;
  properties: PluginProperty[];
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowStep {
  pluginId: string;
  parameters: Record<string, any>;
  order: number;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PluginExecutionRequest {
  pluginId: string;
  parameters: Record<string, any>;
}

export interface WorkflowExecutionRequest {
  workflowId: string;
  initialData?: any;
}

// 前端特有的类型
export interface FormField {
  name: string;
  type: PropertyType;
  label: string;
  required: boolean;
  default?: any;
  description?: string;
  value?: any;
}
