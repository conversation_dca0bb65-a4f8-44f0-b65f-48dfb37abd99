<template>
  <div>
    <NSpace justify="space-between" align="center" style="margin-bottom: 24px">
      <NH2>{{ isEdit ? '编辑插件' : '创建插件' }}</NH2>
      <NSpace>
        <NButton @click="$router.back()">取消</NButton>
        <NButton type="primary" @click="handleSave" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </NButton>
      </NSpace>
    </NSpace>

    <NAlert v-if="error" type="error" style="margin-bottom: 16px" closable @close="clearError">
      {{ error }}
    </NAlert>

    <NGrid :cols="2" :x-gap="24">
      <NGridItem>
        <NCard title="插件信息">
          <NForm ref="formRef" :model="formData" :rules="rules" label-placement="top">
            <NFormItem label="插件名称" path="name">
              <NInput v-model:value="formData.name" placeholder="请输入插件名称" />
            </NFormItem>
            
            <NFormItem label="插件描述" path="description">
              <NInput 
                v-model:value="formData.description" 
                type="textarea" 
                placeholder="请输入插件描述"
                :rows="3"
              />
            </NFormItem>
            
            <NGrid :cols="2" :x-gap="12">
              <NGridItem>
                <NFormItem label="版本" path="version">
                  <NInput v-model:value="formData.version" placeholder="1.0.0" />
                </NFormItem>
              </NGridItem>
              <NGridItem>
                <NFormItem label="作者" path="author">
                  <NInput v-model:value="formData.author" placeholder="请输入作者名称" />
                </NFormItem>
              </NGridItem>
            </NGrid>
          </NForm>
        </NCard>

        <NCard title="属性预览" style="margin-top: 16px">
          <NEmpty v-if="parsedProperties.length === 0" description="暂无属性">
            <template #extra>
              <NText depth="3" size="small">
                在代码中使用 @property 注解定义属性
              </NText>
            </template>
          </NEmpty>
          
          <NList v-else>
            <NListItem v-for="prop in parsedProperties" :key="prop.name">
              <NThing>
                <template #header>
                  <NSpace align="center">
                    <NText strong>{{ prop.label || prop.name }}</NText>
                    <NTag size="small" :type="getTypeColor(prop.type)">
                      {{ prop.type }}
                    </NTag>
                    <NTag v-if="prop.required" size="small" type="error">必填</NTag>
                  </NSpace>
                </template>
                <template #description>
                  <NText depth="3">{{ prop.description || '暂无描述' }}</NText>
                  <div v-if="prop.default !== undefined">
                    <NText depth="3" size="small">默认值: {{ prop.default }}</NText>
                  </div>
                </template>
              </NThing>
            </NListItem>
          </NList>
        </NCard>
      </NGridItem>

      <NGridItem>
        <NCard title="插件代码">
          <div ref="editorContainer" style="height: 600px; border: 1px solid #e0e0e6"></div>
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NH2, NSpace, NButton, NAlert, NGrid, NGridItem, NCard, NForm, NFormItem,
  NInput, NEmpty, NText, NList, NListItem, NThing, NTag,
  useMessage, type FormInst
} from 'naive-ui'
import * as monaco from 'monaco-editor'
import { usePluginStore } from '@/stores/plugin'
import type { PluginProperty } from '@/types'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const pluginStore = usePluginStore()

const { loading, error } = pluginStore
const { getPlugin, createPlugin, updatePlugin, clearError } = pluginStore

const isEdit = computed(() => !!route.params.id)
const formRef = ref<FormInst>()
const editorContainer = ref<HTMLElement>()
let editor: monaco.editor.IStandaloneCodeEditor | null = null

const formData = ref({
  name: '',
  description: '',
  version: '1.0.0',
  author: '',
  code: ''
})

const rules = {
  name: [
    { required: true, message: '请输入插件名称', trigger: 'blur' }
  ]
}

const parsedProperties = ref<PluginProperty[]>([])

// 解析代码中的@property注解
const parseProperties = (code: string): PluginProperty[] => {
  const properties: PluginProperty[] = []
  const regex = /@property\s*\(\s*([^)]+)\s*\)\s*\n?\s*let\s+(\w+)/g
  let match

  while ((match = regex.exec(code)) !== null) {
    const [, params, name] = match
    const property: PluginProperty = {
      name,
      type: 'string',
      label: name,
      required: false
    }

    // 解析参数
    const typeMatch = params.match(/type:\s*['"]?([^'",\s]+)['"]?/)
    if (typeMatch) property.type = typeMatch[1] as any

    const labelMatch = params.match(/label:\s*['"]([^'"]+)['"]/)
    if (labelMatch) property.label = labelMatch[1]

    const requiredMatch = params.match(/required:\s*(true|false)/)
    if (requiredMatch) property.required = requiredMatch[1] === 'true'

    const descMatch = params.match(/description:\s*['"]([^'"]+)['"]/)
    if (descMatch) property.description = descMatch[1]

    const defaultMatch = params.match(/default:\s*(['"]?)([^'",\s]+)\1/)
    if (defaultMatch) {
      const defaultValue = defaultMatch[2]
      switch (property.type) {
        case 'number':
          property.default = parseFloat(defaultValue)
          break
        case 'boolean':
          property.default = defaultValue === 'true'
          break
        default:
          property.default = defaultValue
      }
    }

    properties.push(property)
  }

  return properties
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    string: 'default',
    number: 'info',
    boolean: 'success',
    object: 'warning',
    array: 'error'
  }
  return colors[type] || 'default'
}

// 监听代码变化，实时解析属性
watch(() => formData.value.code, (newCode) => {
  parsedProperties.value = parseProperties(newCode)
}, { immediate: true })

onMounted(async () => {
  // 初始化Monaco编辑器
  if (editorContainer.value) {
    editor = monaco.editor.create(editorContainer.value, {
      value: formData.value.code,
      language: 'typescript',
      theme: 'vs',
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false
    })

    // 监听编辑器内容变化
    editor.onDidChangeModelContent(() => {
      formData.value.code = editor?.getValue() || ''
    })
  }

  // 如果是编辑模式，加载插件数据
  if (isEdit.value && route.params.id) {
    const plugin = await getPlugin(route.params.id as string)
    if (plugin) {
      formData.value = {
        name: plugin.name,
        description: plugin.description,
        version: plugin.version,
        author: plugin.author,
        code: plugin.code
      }
      
      // 更新编辑器内容
      if (editor) {
        editor.setValue(plugin.code)
      }
    }
  } else {
    // 新建模式，设置示例代码
    const exampleCode = `// 示例插件代码
// 使用 @property 注解定义插件参数

@property(type: 'string', label: '输入文本', required: true, description: '需要处理的文本')
let inputText;

@property(type: 'string', label: '操作类型', required: true, default: 'uppercase', description: '处理类型')
let operation;

// 插件生命周期：初始化
async function create() {
  console.log('插件初始化');
}

// 插件生命周期：执行
async function run(input) {
  console.log('开始处理:', inputText);
  
  let result = inputText;
  
  if (operation === 'uppercase') {
    result = result.toUpperCase();
  } else if (operation === 'lowercase') {
    result = result.toLowerCase();
  }
  
  return {
    success: true,
    data: {
      original: inputText,
      processed: result
    },
    logs: [
      '处理完成: ' + result
    ]
  };
}

// 插件生命周期：清理
async function exit() {
  console.log('插件退出');
}`

    formData.value.code = exampleCode
    if (editor) {
      editor.setValue(exampleCode)
    }
  }
})

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    const pluginData = {
      name: formData.value.name,
      description: formData.value.description,
      version: formData.value.version,
      author: formData.value.author,
      code: formData.value.code,
      properties: parsedProperties.value
    }

    let result
    if (isEdit.value && route.params.id) {
      result = await updatePlugin(route.params.id as string, pluginData)
    } else {
      result = await createPlugin(pluginData)
    }

    if (result) {
      message.success(isEdit.value ? '插件更新成功' : '插件创建成功')
      router.push('/plugins')
    }
  } catch (error) {
    // 表单验证失败
  }
}
</script>
