<template>
  <div>
    <NSpace justify="space-between" align="center" style="margin-bottom: 24px">
      <NH2>运行插件</NH2>
      <NButton @click="$router.back()">返回</NButton>
    </NSpace>

    <NAlert v-if="error" type="error" style="margin-bottom: 16px" closable @close="clearError">
      {{ error }}
    </NAlert>

    <NSpin :show="loading">
      <NGrid :cols="2" :x-gap="24" v-if="currentPlugin">
        <NGridItem>
          <NCard :title="currentPlugin.name">
            <template #header-extra>
              <NTag type="info">v{{ currentPlugin.version }}</NTag>
            </template>

            <NSpace vertical>
              <NText>{{ currentPlugin.description || '暂无描述' }}</NText>
              
              <NDivider />

              <NForm ref="formRef" :model="formData" label-placement="top">
                <NFormItem 
                  v-for="prop in currentPlugin.properties" 
                  :key="prop.name"
                  :label="prop.label || prop.name"
                  :path="prop.name"
                  :rule="prop.required ? { required: true, message: `${prop.label || prop.name}不能为空` } : undefined"
                >
                  <template #label>
                    <NSpace align="center">
                      <span>{{ prop.label || prop.name }}</span>
                      <NTag size="small" :type="getTypeColor(prop.type)">{{ prop.type }}</NTag>
                      <NTag v-if="prop.required" size="small" type="error">必填</NTag>
                    </NSpace>
                  </template>

                  <!-- 字符串输入 -->
                  <NInput 
                    v-if="prop.type === 'string'"
                    v-model:value="formData[prop.name]"
                    :placeholder="prop.description || `请输入${prop.label || prop.name}`"
                  />

                  <!-- 数字输入 -->
                  <NInputNumber 
                    v-else-if="prop.type === 'number'"
                    v-model:value="formData[prop.name]"
                    :placeholder="prop.description || `请输入${prop.label || prop.name}`"
                    style="width: 100%"
                  />

                  <!-- 布尔值选择 -->
                  <NSwitch 
                    v-else-if="prop.type === 'boolean'"
                    v-model:value="formData[prop.name]"
                  />

                  <!-- 对象/数组输入 -->
                  <NInput 
                    v-else-if="prop.type === 'object' || prop.type === 'array'"
                    v-model:value="formData[prop.name]"
                    type="textarea"
                    :placeholder="`请输入JSON格式的${prop.type === 'object' ? '对象' : '数组'}`"
                    :rows="3"
                  />

                  <template #feedback v-if="prop.description">
                    <NText depth="3" size="small">{{ prop.description }}</NText>
                  </template>
                </NFormItem>
              </NForm>

              <NSpace justify="end">
                <NButton type="primary" @click="handleRun" :loading="running">
                  <template #icon>
                    <NIcon><PlayIcon /></NIcon>
                  </template>
                  运行插件
                </NButton>
              </NSpace>
            </NSpace>
          </NCard>
        </NGridItem>

        <NGridItem>
          <NCard title="执行结果">
            <NEmpty v-if="!result" description="暂无执行结果">
              <template #extra>
                <NText depth="3" size="small">
                  填写参数并点击"运行插件"按钮
                </NText>
              </template>
            </NEmpty>

            <div v-else>
              <NAlert 
                :type="result.success ? 'success' : 'error'" 
                style="margin-bottom: 16px"
              >
                <template #header>
                  {{ result.success ? '执行成功' : '执行失败' }}
                </template>
                <div v-if="!result.success && result.error">
                  错误信息: {{ result.error }}
                </div>
              </NAlert>

              <NTabs type="line">
                <NTabPane name="data" tab="输出数据">
                  <NCode 
                    :code="JSON.stringify(result.data, null, 2)" 
                    language="json"
                    show-line-numbers
                  />
                </NTabPane>

                <NTabPane name="logs" tab="执行日志">
                  <NList>
                    <NListItem v-for="(log, index) in result.logs" :key="index">
                      <NText 
                        :type="getLogType(log)"
                        family="mono"
                        size="small"
                      >
                        {{ log }}
                      </NText>
                    </NListItem>
                  </NList>
                </NTabPane>

                <NTabPane name="metadata" tab="执行信息">
                  <NDescriptions :column="1" bordered>
                    <NDescriptionsItem label="执行时间">
                      {{ result.metadata.executionTime }}ms
                    </NDescriptionsItem>
                    <NDescriptionsItem label="执行时间戳">
                      {{ formatDate(result.metadata.timestamp) }}
                    </NDescriptionsItem>
                  </NDescriptions>
                </NTabPane>
              </NTabs>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </NSpin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  NH2, NSpace, NButton, NAlert, NSpin, NGrid, NGridItem, NCard, NTag, NText,
  NDivider, NForm, NFormItem, NInput, NInputNumber, NSwitch, NIcon, NEmpty,
  NTabs, NTabPane, NCode, NList, NListItem, NDescriptions, NDescriptionsItem,
  useMessage, type FormInst
} from 'naive-ui'
import { usePluginStore } from '@/stores/plugin'
import type { PluginResult } from '@/types'

const PlayIcon = () => '▶'

const route = useRoute()
const message = useMessage()
const pluginStore = usePluginStore()

const { currentPlugin, loading, error } = pluginStore
const { getPlugin, runPlugin, clearError } = pluginStore

const formRef = ref<FormInst>()
const formData = ref<Record<string, any>>({})
const running = ref(false)
const result = ref<PluginResult | null>(null)

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    string: 'default',
    number: 'info',
    boolean: 'success',
    object: 'warning',
    array: 'error'
  }
  return colors[type] || 'default'
}

const getLogType = (log: string) => {
  if (log.includes('[ERROR]')) return 'error'
  if (log.includes('[WARN]')) return 'warning'
  if (log.includes('[SYSTEM]')) return 'info'
  return 'default'
}

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN')
}

onMounted(async () => {
  const pluginId = route.params.id as string
  if (pluginId) {
    const plugin = await getPlugin(pluginId)
    if (plugin) {
      // 初始化表单数据
      const initialData: Record<string, any> = {}
      plugin.properties.forEach(prop => {
        if (prop.default !== undefined) {
          initialData[prop.name] = prop.default
        } else {
          // 根据类型设置默认值
          switch (prop.type) {
            case 'string':
              initialData[prop.name] = ''
              break
            case 'number':
              initialData[prop.name] = 0
              break
            case 'boolean':
              initialData[prop.name] = false
              break
            case 'object':
              initialData[prop.name] = '{}'
              break
            case 'array':
              initialData[prop.name] = '[]'
              break
          }
        }
      })
      formData.value = initialData
    }
  }
})

const handleRun = async () => {
  try {
    await formRef.value?.validate()
    
    running.value = true
    result.value = null

    // 处理对象和数组类型的参数
    const processedData: Record<string, any> = {}
    for (const [key, value] of Object.entries(formData.value)) {
      const prop = currentPlugin.value?.properties.find(p => p.name === key)
      if (prop && (prop.type === 'object' || prop.type === 'array')) {
        try {
          processedData[key] = JSON.parse(value as string)
        } catch (e) {
          message.error(`参数 ${prop.label || key} 的JSON格式不正确`)
          return
        }
      } else {
        processedData[key] = value
      }
    }

    const pluginResult = await runPlugin(route.params.id as string, processedData)
    if (pluginResult) {
      result.value = pluginResult
      if (pluginResult.success) {
        message.success('插件执行成功')
      } else {
        message.error('插件执行失败')
      }
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    running.value = false
  }
}
</script>
