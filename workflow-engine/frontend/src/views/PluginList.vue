<template>
  <div>
    <NSpace justify="space-between" align="center" style="margin-bottom: 24px">
      <NH2>插件管理</NH2>
      <NButton type="primary" @click="$router.push('/plugins/create')">
        <template #icon>
          <NIcon><AddIcon /></NIcon>
        </template>
        创建插件
      </NButton>
    </NSpace>

    <NAlert v-if="error" type="error" style="margin-bottom: 16px" closable @close="clearError">
      {{ error }}
    </NAlert>

    <NCard>
      <NSpin :show="loading">
        <NEmpty v-if="!loading && plugins.length === 0" description="暂无插件">
          <template #extra>
            <NButton size="small" @click="$router.push('/plugins/create')">
              创建第一个插件
            </NButton>
          </template>
        </NEmpty>

        <NList v-else>
          <NListItem v-for="plugin in plugins" :key="plugin.id">
            <NThing>
              <template #header>
                <NSpace align="center">
                  <NText strong>{{ plugin.name }}</NText>
                  <NTag size="small" type="info">v{{ plugin.version }}</NTag>
                </NSpace>
              </template>
              
              <template #description>
                <NText depth="3">{{ plugin.description || '暂无描述' }}</NText>
              </template>

              <template #footer>
                <NSpace>
                  <NText depth="3" size="small">
                    作者: {{ plugin.author || '未知' }}
                  </NText>
                  <NDivider vertical />
                  <NText depth="3" size="small">
                    属性: {{ plugin.properties.length }} 个
                  </NText>
                  <NDivider vertical />
                  <NText depth="3" size="small">
                    创建时间: {{ formatDate(plugin.createdAt) }}
                  </NText>
                </NSpace>
              </template>

              <template #action>
                <NSpace>
                  <NButton size="small" @click="runPlugin(plugin)">
                    <template #icon>
                      <NIcon><PlayIcon /></NIcon>
                    </template>
                    运行
                  </NButton>
                  <NButton size="small" @click="editPlugin(plugin)">
                    <template #icon>
                      <NIcon><EditIcon /></NIcon>
                    </template>
                    编辑
                  </NButton>
                  <NPopconfirm @positive-click="handleDelete(plugin.id)">
                    <template #trigger>
                      <NButton size="small" type="error">
                        <template #icon>
                          <NIcon><DeleteIcon /></NIcon>
                        </template>
                        删除
                      </NButton>
                    </template>
                    确定要删除插件 "{{ plugin.name }}" 吗？
                  </NPopconfirm>
                </NSpace>
              </template>
            </NThing>
          </NListItem>
        </NList>
      </NSpin>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  NH2, NSpace, NButton, NIcon, NAlert, NCard, NSpin, NEmpty, 
  NList, NListItem, NThing, NText, NTag, NDivider, NPopconfirm,
  useMessage
} from 'naive-ui'
import { usePluginStore } from '@/stores/plugin'
import type { PluginMetadata } from '@/types'

// 图标组件 (这里用简单的文本代替，实际项目中可以使用图标库)
const AddIcon = () => '+'
const PlayIcon = () => '▶'
const EditIcon = () => '✏'
const DeleteIcon = () => '🗑'

const router = useRouter()
const message = useMessage()
const pluginStore = usePluginStore()

const { plugins, loading, error } = pluginStore
const { fetchPlugins, deletePlugin, clearError } = pluginStore

onMounted(() => {
  fetchPlugins()
})

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const runPlugin = (plugin: PluginMetadata) => {
  router.push(`/plugins/${plugin.id}/run`)
}

const editPlugin = (plugin: PluginMetadata) => {
  router.push(`/plugins/${plugin.id}/edit`)
}

const handleDelete = async (id: string) => {
  const success = await deletePlugin(id)
  if (success) {
    message.success('插件删除成功')
  } else {
    message.error('插件删除失败')
  }
}
</script>
