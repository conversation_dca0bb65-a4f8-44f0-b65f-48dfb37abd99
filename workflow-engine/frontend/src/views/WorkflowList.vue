<template>
  <div>
    <NSpace justify="space-between" align="center" style="margin-bottom: 24px">
      <NH2>工作流管理</NH2>
      <NButton type="primary" disabled>
        <template #icon>
          <NIcon><AddIcon /></NIcon>
        </template>
        创建工作流
      </NButton>
    </NSpace>

    <NCard>
      <NEmpty description="工作流功能开发中">
        <template #extra>
          <NText depth="3" size="small">
            工作流功能将在后续版本中实现，敬请期待！
          </NText>
        </template>
      </NEmpty>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { NH2, NSpace, NButton, NIcon, NCard, NEmpty, NText } from 'naive-ui'

const AddIcon = () => '+'
</script>
