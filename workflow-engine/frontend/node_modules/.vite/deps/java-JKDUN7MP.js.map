{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/java/java.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/java/java.ts\nvar conf = {\n  // the default separators except `@$`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".java\",\n  keywords: [\n    \"abstract\",\n    \"continue\",\n    \"for\",\n    \"new\",\n    \"switch\",\n    \"assert\",\n    \"default\",\n    \"goto\",\n    \"package\",\n    \"synchronized\",\n    \"boolean\",\n    \"do\",\n    \"if\",\n    \"private\",\n    \"this\",\n    \"break\",\n    \"double\",\n    \"implements\",\n    \"protected\",\n    \"throw\",\n    \"byte\",\n    \"else\",\n    \"import\",\n    \"public\",\n    \"throws\",\n    \"case\",\n    \"enum\",\n    \"instanceof\",\n    \"return\",\n    \"transient\",\n    \"catch\",\n    \"extends\",\n    \"int\",\n    \"short\",\n    \"try\",\n    \"char\",\n    \"final\",\n    \"interface\",\n    \"static\",\n    \"void\",\n    \"class\",\n    \"finally\",\n    \"long\",\n    \"strictfp\",\n    \"volatile\",\n    \"const\",\n    \"float\",\n    \"native\",\n    \"super\",\n    \"while\",\n    \"true\",\n    \"false\",\n    \"yield\",\n    \"record\",\n    \"sealed\",\n    \"non-sealed\",\n    \"permits\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"!\",\n    \"~\",\n    \"?\",\n    \":\",\n    \"==\",\n    \"<=\",\n    \">=\",\n    \"!=\",\n    \"&&\",\n    \"||\",\n    \"++\",\n    \"--\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"&\",\n    \"|\",\n    \"^\",\n    \"%\",\n    \"<<\",\n    \">>\",\n    \">>>\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"%=\",\n    \"<<=\",\n    \">>=\",\n    \">>>=\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // Special keyword with a dash\n      [\"non-sealed\", \"keyword.non-sealed\"],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // @ annotations.\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, \"annotation\"],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\"],\n      [/0(@octaldigits)[Ll]?/, \"number.octal\"],\n      [/0[bB](@binarydigits)[Ll]?/, \"number.binary\"],\n      [/(@digits)[fFdD]/, \"number.float\"],\n      [/(@digits)[lL]?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"\"\"/, \"string\", \"@multistring\"],\n      [/\"/, \"string\", \"@string\"],\n      // characters\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@javadoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      // [/\\/\\*/, 'comment', '@push' ],    // nested comment not allowed :-(\n      // [/\\/\\*/,    'comment.invalid' ],    // this breaks block comments in the shape of /* //*/\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    //Identical copy of comment above, except for the addition of .doc\n    javadoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      // [/\\/\\*/, 'comment.doc', '@push' ],    // nested comment not allowed :-(\n      [/\\/\\*/, \"comment.doc.invalid\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    multistring: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"\"\"/, \"string\", \"@pop\"],\n      [/./, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,oDAAoD;AAAA,MACtE,KAAK,IAAI,OAAO,sDAAsD;AAAA,IACxE;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,cAAc,oBAAoB;AAAA;AAAA,MAEnC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,0BAA0B,YAAY;AAAA;AAAA,MAEvC,CAAC,0CAA0C,cAAc;AAAA,MACzD,CAAC,qDAAqD,cAAc;AAAA,MACpE,CAAC,0BAA0B,YAAY;AAAA,MACvC,CAAC,wBAAwB,cAAc;AAAA,MACvC,CAAC,6BAA6B,eAAe;AAAA,MAC7C,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,kBAAkB,QAAQ;AAAA;AAAA,MAE3B,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,OAAO,UAAU,cAAc;AAAA,MAChC,CAAC,KAAK,UAAU,SAAS;AAAA;AAAA,MAEzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,UAAU;AAAA,MAC1C,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA;AAAA;AAAA,MAGrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,WAAW,aAAa;AAAA;AAAA,MAEzB,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IACzB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,MACX,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,OAAO,UAAU,MAAM;AAAA,MACxB,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;", "names": []}