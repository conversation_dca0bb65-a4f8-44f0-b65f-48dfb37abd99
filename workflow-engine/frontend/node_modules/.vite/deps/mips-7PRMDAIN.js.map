{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/mips/mips.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/mips/mips.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\$\\-\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    blockComment: [\"###\", \"###\"],\n    lineComment: \"#\"\n  },\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: false,\n  tokenPostfix: \".mips\",\n  regEx: /\\/(?!\\/\\/)(?:[^\\/\\\\]|\\\\.)*\\/[igm]*/,\n  keywords: [\n    \".data\",\n    \".text\",\n    \"syscall\",\n    \"trap\",\n    \"add\",\n    \"addu\",\n    \"addi\",\n    \"addiu\",\n    \"and\",\n    \"andi\",\n    \"div\",\n    \"divu\",\n    \"mult\",\n    \"multu\",\n    \"nor\",\n    \"or\",\n    \"ori\",\n    \"sll\",\n    \"slv\",\n    \"sra\",\n    \"srav\",\n    \"srl\",\n    \"srlv\",\n    \"sub\",\n    \"subu\",\n    \"xor\",\n    \"xori\",\n    \"lhi\",\n    \"lho\",\n    \"lhi\",\n    \"llo\",\n    \"slt\",\n    \"slti\",\n    \"sltu\",\n    \"sltiu\",\n    \"beq\",\n    \"bgtz\",\n    \"blez\",\n    \"bne\",\n    \"j\",\n    \"jal\",\n    \"jalr\",\n    \"jr\",\n    \"lb\",\n    \"lbu\",\n    \"lh\",\n    \"lhu\",\n    \"lw\",\n    \"li\",\n    \"la\",\n    \"sb\",\n    \"sh\",\n    \"sw\",\n    \"mfhi\",\n    \"mflo\",\n    \"mthi\",\n    \"mtlo\",\n    \"move\"\n  ],\n  // we include these common regular expressions\n  symbols: /[\\.,\\:]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [/\\$[a-zA-Z_]\\w*/, \"variable.predefined\"],\n      [\n        /[.a-zA-Z_]\\w*/,\n        {\n          cases: {\n            this: \"variable.predefined\",\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // Comments\n      [/#.*$/, \"comment\"],\n      // regular expressions\n      [\"///\", { token: \"regexp\", next: \"@hereregexp\" }],\n      [/^(\\s*)(@regEx)/, [\"\", \"regexp\"]],\n      [/(\\,)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      [/(\\:)(\\s*)(@regEx)/, [\"delimiter\", \"\", \"regexp\"]],\n      // delimiters\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d+[eE]([\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F]+/, \"number.hex\"],\n      [/0[0-7]+(?!\\d)/, \"number.octal\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[,.]/, \"delimiter\"],\n      // strings:\n      [/\"\"\"/, \"string\", '@herestring.\"\"\"'],\n      [/'''/, \"string\", \"@herestring.'''\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [/[^\"'\\#\\\\]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/\\./, \"string.escape.invalid\"],\n      [\n        /#{/,\n        {\n          cases: {\n            '$S2==\"': {\n              token: \"string\",\n              next: \"root.interpolatedstring\"\n            },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/#/, \"string\"]\n    ],\n    herestring: [\n      [\n        /(\"\"\"|''')/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^#\\\\'\"]+/, \"string\"],\n      [/['\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\./, \"string.escape.invalid\"],\n      [/#{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/#/, \"string\"]\n    ],\n    comment: [\n      [/[^#]+/, \"comment\"],\n      [/#/, \"comment\"]\n    ],\n    hereregexp: [\n      [/[^\\\\\\/#]+/, \"regexp\"],\n      [/\\\\./, \"regexp\"],\n      [/#.*$/, \"comment\"],\n      [\"///[igm]*\", { token: \"regexp\", next: \"@pop\" }],\n      [/\\//, \"regexp\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,OAAO,KAAK;AAAA,IAC3B,aAAa;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACtC;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,OAAO;AAAA,EACP,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,kBAAkB,qBAAqB;AAAA,MACxC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,MAAM;AAAA,YACN,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,cAAc,CAAC;AAAA,MAChD,CAAC,kBAAkB,CAAC,IAAI,QAAQ,CAAC;AAAA,MACjC,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA,MACjD,CAAC,qBAAqB,CAAC,aAAa,IAAI,QAAQ,CAAC;AAAA;AAAA,MAEjD,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,uBAAuB,cAAc;AAAA,MACtC,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,qBAAqB,YAAY;AAAA,MAClC,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,QAAQ,WAAW;AAAA;AAAA,MAEpB,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC,CAAC,OAAO,UAAU,iBAAiB;AAAA,MACnC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,cAAc,QAAQ;AAAA,MACvB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,uBAAuB;AAAA,MAC9B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU;AAAA,cACR,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B,CAAC,MAAM,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,MACjE,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,SAAS,SAAS;AAAA,MACnB,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,QAAQ,SAAS;AAAA,MAClB,CAAC,aAAa,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MAC/C,CAAC,MAAM,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;", "names": []}